#!/usr/bin/env python3
"""
Debug script to analyze delta and distance calculation issues in OPTIONS scanning.
"""

import sys
import os
import pandas as pd
import logging
from typing import Dict, List

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader
from market_type_scanner import OptionsScanner
from fyers_connect import FyersConnect

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_options_scanning():
    """Debug the OPTIONS scanning process to identify delta and distance issues."""
    
    print("=== DEBUG: OPTIONS Delta and Distance Calculation ===")
    
    # Load configuration
    config = ConfigLoader()
    print(f"Pivot point enabled: {config.pivot_point_enabled}")
    print(f"Delta range: {config.min_delta} - {config.max_delta}")
    print(f"Symbols to process: {config.symbols}")
    
    # Create scanner
    scanner = OptionsScanner(config)
    
    # Authenticate
    if not scanner.authenticate_fyers():
        print("ERROR: Authentication failed")
        return
    
    print("\n=== Step 1: Get symbols for scanning ===")
    symbols_to_scan = scanner.get_symbols_for_scanning(['RELIANCE'])  # Test with RELIANCE which has options
    print(f"Symbols to scan: {len(symbols_to_scan)}")
    for i, symbol in enumerate(symbols_to_scan[:5]):  # Show first 5
        print(f"  {i+1}. {symbol}")

    # Check if delta map was created
    if hasattr(scanner, '_delta_map'):
        print(f"\nDelta map created with {len(scanner._delta_map)} entries")
        for symbol, delta in list(scanner._delta_map.items())[:5]:
            print(f"  {symbol}: {delta}")
    else:
        print("\nWARNING: No delta map found!")

    if len(symbols_to_scan) == 0:
        print("No symbols to scan, exiting debug")
        return

    print("\n=== Step 2: Fetch market data ===")
    market_data = scanner.fetch_market_data(symbols_to_scan[:5])  # Test with first 5 symbols
    print(f"Market data fetched for {len(market_data)} symbols")

    for symbol, data in list(market_data.items())[:3]:
        print(f"  {symbol}: LTP={data.ltp}, Volume={data.volume}")

    print("\n=== Step 3: Apply market filters ===")
    filtered_market_data = scanner.apply_market_specific_filters(market_data)
    print(f"Filtered market data: {len(filtered_market_data)} symbols")

    print("\n=== Step 3.5: Apply pivot point filter ===")
    # Enable debug logging for pivot point integration
    logging.getLogger('pivot_point_integration').setLevel(logging.DEBUG)

    pivot_filtered_data = scanner.apply_pivot_point_filter(filtered_market_data)
    print(f"Pivot filtered data: {len(pivot_filtered_data)} symbols")

    # Check if pivot data was added
    for symbol, data in list(pivot_filtered_data.items())[:3]:
        print(f"  {symbol}: has_pivot_data={hasattr(data, 'pivot_data')}")
        if hasattr(data, 'pivot_data') and data.pivot_data:
            print(f"    Pivot data exists: {data.pivot_data.min_positive_pivot_level}")

    # Update filtered_market_data to use the pivot-filtered data
    filtered_market_data = pivot_filtered_data

    print("\n=== Step 4: Create filtered symbols ===")

    # Debug: Check what symbols are in filtered market data
    print("Symbols in filtered market data:")
    for symbol, data in filtered_market_data.items():
        print(f"  {symbol}: LTP={data.ltp}, has_pivot_data={hasattr(data, 'pivot_data')}")

    # Enable debug logging temporarily
    logging.getLogger('market_type_scanner').setLevel(logging.DEBUG)
    logging.getLogger('universal_symbol_parser').setLevel(logging.DEBUG)

    # Debug: Test symbol parsing manually
    print("\nTesting symbol parsing:")
    csv_file = scanner.config.get_csv_file_for_market_type('OPTIONS')
    print(f"CSV file for OPTIONS: {csv_file}")

    # Check if CSV file exists and has data
    import os
    if os.path.exists(csv_file):
        print(f"CSV file exists: {csv_file}")
        # Check if our symbols are in the CSV
        test_symbol = "NSE:RELIANCE25JUL1420CE"
        with open(csv_file, 'r') as f:
            content = f.read()
            if test_symbol in content:
                print(f"Test symbol {test_symbol} found in CSV file")
            else:
                print(f"Test symbol {test_symbol} NOT found in CSV file")
    else:
        print(f"CSV file does not exist: {csv_file}")

    for symbol in list(filtered_market_data.keys())[:3]:
        print(f"\nTesting symbol: {symbol}")
        parsed_symbol = scanner.symbol_parser.parse_symbol(symbol, csv_file)
        print(f"  parsed={parsed_symbol is not None}")
        if parsed_symbol:
            print(f"    underlying={parsed_symbol.underlying}, strike={parsed_symbol.strike_price}, type={parsed_symbol.option_type}")
        else:
            print(f"    Failed to parse symbol {symbol}")

    filtered_symbols = scanner.convert_to_filtered_symbols(filtered_market_data)
    print(f"Filtered symbols created: {len(filtered_symbols)}")

    for symbol in filtered_symbols[:3]:
        print(f"  Symbol: {symbol.symbol}")
        print(f"    Delta: {getattr(symbol, 'delta', 'NOT_SET')}")
        print(f"    Has pivot_data: {hasattr(symbol, 'pivot_data')}")
        if hasattr(symbol, 'pivot_data') and symbol.pivot_data:
            pivot_data = symbol.pivot_data
            print(f"    Min positive pivot level: {pivot_data.min_positive_pivot_level}")
            print(f"    Min positive pivot value: {pivot_data.min_positive_pivot_value}")
            print(f"    Distance to min positive pivot: {pivot_data.distance_to_min_positive_pivot}")
            print(f"    Distance percentage: {pivot_data.distance_to_min_positive_pivot_pct}")
        print()

if __name__ == "__main__":
    debug_options_scanning()
