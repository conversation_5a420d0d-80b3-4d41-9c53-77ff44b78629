# General Settings
general:
  env_path: '../.env' # Path means (./.env)= current directory, (../.env)=parent directory
  output_dir: 'reports'
  fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv', 'https://public.fyers.in/sym_details/NSE_FO.csv']

market_types:
  #- EQUITY
  #- INDEX
  #- FUTURES
  - OPTIONS

# Trading Symbols
symbols:
  #- 'ALL'
  #- 'NIFTY50'
  #- 'NIFTY'
  #- 'BANKNIFTY'
  #- 'FINNIFTY'
  #- 'MIDCPNIFTY'
  #- 'RELIANCE'
  #- 'BEL'
  #- 'DIXON'  
  #- 'MCX'
  #- 'BSE'
  #- 'KALYANKJIL'
  - 'ASTRAL'
  - 'BPCL'
  - 'BIOCON'
  - 'MUTHOOTFIN'
  - 'MANAPPURAM'
  - 'IGL'
  - 'PPLPHARMA'
  - 'CROMPTON'
  - 'HDFCLIFE'
  - 'DIXON'


# Market Filter Settings
market_filters:
  min_volume: 11
  max_volume: ***********
  min_ltp_price: 1
  max_ltp_price: 100000.0

# Timeframe Settings
timeframe:           # When you change 60 mins to 1D, you need to change days_to_fetch to 30, otherwise do not change it.
  interval: 60       # As shown in interval map 60 in minutes, 1D for days 
  days_to_fetch: 15  # Days - Increased for better MAE accuracy

# Options Filter Settings 
options_filter:
  #target_months: ['JUL'] #['JUL', 'AUG', 'SEP']
  strike_level: 20    # P1 & P2 & P3: Pivot Point,SELLING & HFT Strike level from ATM for both CE and PE
  min_delta: 0.27     # P1: Pivot Point is calculated based on delta
  max_delta: 0.64     # P1: Pivot Point is calculated based on delta
  
# P2 & P3 Settings
ce_pe_pairing:
  enabled: false
  min_price_percent: 0.0
  max_price_percent: 2.0

# Moving Average Exponential Indicator Settings (using ta library)
mae_indicator:
  enabled: false            # If there are no symbols found switch off as false
  length: 9                # Period for the MAE
  source: 'close'          # Options: open, high, low, close, hl2, hlc3, ohlc4
  offset: 0                # Can be positive or negative
  smoothing_enabled: false # If true then uses SMA/EMA/WMA to smooth the indicator values
  smoothing_line: 'sma'    # Options: 'sma', 'ema', 'wma' (as per ta library)
  smoothing_length: 9      # Only positive value

pivot_point_indicator:
  enabled: true             # If there are no symbols found switch off as false
  calculation_type: 'WEEKLY' # 'DAILY', 'WEEKLY', 'MONTHLY'
  top_n_closest: 30           # Number of closest pivot points to consider

# Rate Limiting Settings / DO NOT CHANGE this, otherwise it will be a problem and slow down the process.
rate_limit:
  min_delay_seconds: 0.1   # Minimum delay between API requests (seconds) - optimized for performance
  max_retries: 5           # Number of retries on 429 error - increased for reliability
  retry_backoff: 3.0       # Base seconds for exponential backoff on retry

