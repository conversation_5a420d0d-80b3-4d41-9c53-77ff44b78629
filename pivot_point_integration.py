"""
Pivot Point Integration Module for Unified Scanner.
Provides pivot point calculation and filtering functionality that can be integrated
into the unified scanner while maintaining compatibility with existing CSV structure.
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple, TYPE_CHECKING
from dataclasses import dataclass

from config_loader import Config<PERSON>oader
from pivot_points import calculate_pivot_points as calc_pivot_points, _calculate_pivot_standard
from fyers_client import FyersClient

if TYPE_CHECKING:
    from market_type_scanner import FilteredSymbol, MarketData

logger = logging.getLogger(__name__)

@dataclass
class PivotPointData:
    """Data class for pivot point information."""
    pivot_levels: Dict[str, float]
    closest_positive_pivot_level: Optional[str] = None
    closest_positive_pivot_value: Optional[float] = None
    distance_to_positive_pivot: Optional[float] = None
    distance_to_positive_pivot_pct: Optional[float] = None
    min_positive_pivot_level: Optional[str] = None
    min_positive_pivot_value: Optional[float] = None
    distance_to_min_positive_pivot: Optional[float] = None
    distance_to_min_positive_pivot_pct: Optional[float] = None

class PivotPointIntegration:
    """
    Pivot Point Integration class that provides pivot point functionality
    for the unified scanner.
    """
    
    def __init__(self, config: ConfigLoader, fyers_client: FyersClient):
        """
        Initialize the pivot point integration.

        Args:
            config: Configuration loader instance
            fyers_client: Authenticated Fyers client instance
        """
        self.config = config
        self.fyers_client = fyers_client
        self.enabled = config.pivot_point_enabled
        self.calculation_type = config.pivot_point_calculation_type
        self.top_n_closest = config.pivot_point_top_n_closest

        # Cache FyersConnect instance for OHLC fetching
        self._fyers_connect = None

        # Cache for OHLC data to avoid repeated API calls
        self._ohlc_cache = {}
        self._failed_symbols = set()  # Track symbols that failed to get OHLC data

        logger.info(f"Pivot Point Integration initialized - Enabled: {self.enabled}, Type: {self.calculation_type}")
    
    def is_enabled(self) -> bool:
        """Check if pivot point integration is enabled."""
        return self.enabled
    
    def calculate_pivot_points_for_symbol(self, symbol: str, market_data: Any) -> Optional[PivotPointData]:
        """
        Calculate pivot points for a single symbol using its market data.

        Args:
            symbol: Trading symbol
            market_data: Market data for the symbol

        Returns:
            PivotPointData object or None if calculation fails
        """
        if not self.enabled:
            return None

        try:
            # For options symbols, fetch individual option OHLC data (same as pivot_point_core.py)
            if self._is_options_symbol(symbol):
                return self._calculate_pivot_points_for_option_with_ohlc(symbol, market_data)

            # For non-options symbols (EQUITY, INDEX, FUTURES), fetch weekly OHLC data
            return self._calculate_pivot_points_for_non_option_with_ohlc(symbol, market_data)

        except Exception as e:
            logger.warning(f"Failed to calculate pivot points for {symbol}: {e}")
            return None

    def _is_options_symbol(self, symbol: str) -> bool:
        """Check if symbol is an options symbol."""
        return ('CE' in symbol or 'PE' in symbol) and any(month in symbol for month in ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'])

    def _calculate_pivot_points_for_option_with_ohlc(self, symbol: str, market_data: Any) -> Optional[PivotPointData]:
        """Calculate pivot points for options using individual option OHLC data based on calculation_type."""
        try:
            # Fetch OHLC data for this specific option based on calculation_type
            ohlc_data = self._fetch_ohlc_for_option(symbol)

            # Fallback: If historical OHLC data is not available, use current market data
            if not ohlc_data or not all(key in ohlc_data for key in ['high', 'low', 'close']):
                logger.debug(f"No historical OHLC data for option {symbol}, using current market data as fallback")

                # Use current market data OHLC values
                high = market_data.high_price if hasattr(market_data, 'high_price') else 0
                low = market_data.low_price if hasattr(market_data, 'low_price') else 0
                close = market_data.close_price if hasattr(market_data, 'close_price') else 0

                # Additional fallback: if market data OHLC is invalid, create synthetic OHLC based on LTP
                if high <= 0 or low <= 0 or close <= 0 or (high == low == close):
                    ltp = market_data.ltp if hasattr(market_data, 'ltp') else 0
                    if ltp > 0:
                        # Create synthetic OHLC with small variations around LTP
                        # This provides more realistic pivot calculations than using identical values
                        variation = max(0.05, ltp * 0.02)  # 2% variation or minimum 0.05
                        high = ltp + variation
                        low = ltp - variation
                        close = ltp
                        logger.debug(f"Created synthetic OHLC for {symbol} based on LTP {ltp}: H={high}, L={low}, C={close}")
                    else:
                        logger.warning(f"Invalid market data and LTP for option {symbol}")
                        return None
                else:
                    logger.debug(f"Using market data OHLC for option {symbol}: H={high}, L={low}, C={close}")
            else:
                high = ohlc_data.get('high', 0)
                low = ohlc_data.get('low', 0)
                close = ohlc_data.get('close', 0)

                # Check if historical OHLC data is valid
                if high <= 0 or low <= 0 or close <= 0:
                    logger.warning(f"Invalid historical OHLC values for option {symbol}: H={high}, L={low}, C={close}")
                    return None

                # Check if historical OHLC data has all identical values (indicates stale/invalid data)
                if high == low == close:
                    logger.warning(f"Historical OHLC data has identical values for {symbol}: H={high}, L={low}, C={close}. "
                                 f"This may indicate stale data.")
                    # Try to use market data as fallback
                    market_high = market_data.high_price if hasattr(market_data, 'high_price') else 0
                    market_low = market_data.low_price if hasattr(market_data, 'low_price') else 0
                    market_close = market_data.close_price if hasattr(market_data, 'close_price') else 0

                    if market_high > 0 and market_low > 0 and market_close > 0 and not (market_high == market_low == market_close):
                        high, low, close = market_high, market_low, market_close
                        logger.debug(f"Switched to market data OHLC for {symbol}: H={high}, L={low}, C={close}")
                    else:
                        logger.debug(f"Market data also has identical values, keeping historical data for {symbol}")
                        # Keep the historical data even if identical - pivot calculation will handle it

            # Validate OHLC spread - ensure we have realistic price variation
            price_range = high - low
            avg_price = (high + low + close) / 3
            spread_percentage = (price_range / avg_price * 100) if avg_price > 0 else 0

            # If all OHLC values are the same (no spread), this indicates no actual trading activity
            if price_range == 0:
                logger.warning(f"No price variation in OHLC data for {symbol}: H={high}, L={low}, C={close}. "
                             f"This indicates no actual trading activity. Skipping pivot calculation.")
                return None
            elif spread_percentage < 0.1:  # Less than 0.1% spread might indicate stale data
                logger.debug(f"Very low price spread ({spread_percentage:.3f}%) for {symbol}. "
                           f"Pivot levels may be very close to each other.")

            # Calculate pivot points using the standard formula
            pivot_levels = _calculate_pivot_standard(high, low, close)

            # Validate pivot point calculation results
            pivot_value = pivot_levels.get('Pivot', 0)
            if pivot_value > 0:
                # Check if pivot levels show reasonable variation
                r1_value = pivot_levels.get('R1', 0)
                s1_value = pivot_levels.get('S1', 0)
                pivot_spread = abs(r1_value - s1_value) if r1_value > 0 and s1_value > 0 else 0
                pivot_spread_pct = (pivot_spread / pivot_value * 100) if pivot_value > 0 else 0

                if pivot_spread_pct < 0.5:  # Less than 0.5% spread between R1 and S1
                    logger.warning(f"Very narrow pivot spread ({pivot_spread_pct:.3f}%) for {symbol}. "
                                 f"This may indicate insufficient price variation in OHLC data.")

            # Create pivot point data object
            pivot_data = PivotPointData(pivot_levels=pivot_levels)

            # Calculate closest positive pivot information using current LTP
            self._calculate_closest_positive_pivot(market_data.ltp, pivot_data)

            # Calculate minimum positive pivot information
            self._calculate_min_positive_pivot(market_data.ltp, pivot_data)

            logger.debug(f"Calculated {self.calculation_type} pivot points for option {symbol} using OHLC: {pivot_levels}")
            return pivot_data

        except Exception as e:
            logger.warning(f"Failed to calculate pivot points for option {symbol}: {e}")
            return None

    def _calculate_pivot_points_for_non_option_with_ohlc(self, symbol: str, market_data: Any) -> Optional[PivotPointData]:
        """Calculate pivot points for non-options (EQUITY, INDEX, FUTURES) based on calculation_type."""
        try:
            # Fetch OHLC data for this specific symbol based on calculation_type
            ohlc_data = self._fetch_ohlc_for_non_option(symbol)

            if not ohlc_data or not all(key in ohlc_data for key in ['high', 'low', 'close']):
                logger.warning(f"Invalid OHLC data for {symbol}: {ohlc_data}")
                return None

            high = ohlc_data.get('high', 0)
            low = ohlc_data.get('low', 0)
            close = ohlc_data.get('close', 0)

            if high <= 0 or low <= 0 or close <= 0:
                logger.warning(f"Invalid OHLC values for {symbol}: H={high}, L={low}, C={close}")
                return None

            # Calculate pivot points using the standard formula
            pivot_levels = _calculate_pivot_standard(high, low, close)

            # Create pivot point data object
            pivot_data = PivotPointData(pivot_levels=pivot_levels)

            # Calculate closest positive pivot information using current LTP
            self._calculate_closest_positive_pivot(market_data.ltp, pivot_data)

            # Calculate minimum positive pivot information
            self._calculate_min_positive_pivot(market_data.ltp, pivot_data)

            logger.debug(f"Calculated {self.calculation_type} pivot points for {symbol} using OHLC: {pivot_levels}")
            return pivot_data

        except Exception as e:
            logger.warning(f"Failed to calculate pivot points for {symbol}: {e}")
            return None

    def _fetch_ohlc_for_non_option(self, symbol: str) -> Optional[Dict[str, float]]:
        """Fetch OHLC data for non-option symbols (EQUITY, INDEX, FUTURES) based on calculation_type."""
        try:
            # Get cached FyersConnect instance
            fyers_connect = self._get_fyers_connect()
            if not fyers_connect:
                return None

            # Extract underlying symbol from NSE format based on symbol type
            if '-INDEX' in symbol:
                # For INDEX symbols, extract just the underlying (e.g., NSE:NIFTY50-INDEX -> NIFTY50)
                underlying_symbol = symbol.replace('NSE:', '').replace('-INDEX', '')
            else:
                # For EQUITY and FUTURES, extract underlying (e.g., NSE:RELIANCE-EQ -> RELIANCE)
                underlying_symbol = symbol.replace('NSE:', '').split('-')[0].split('FUT')[0]

            # Fetch OHLC data based on calculation_type
            if self.calculation_type == "WEEKLY":
                # Keep existing weekly functionality unchanged
                weekly_ohlc_df = fyers_connect.get_weekly_pivot_ohlc_data(underlying_symbol)
                if not weekly_ohlc_df.empty:
                    ohlc_dict = {
                        'high': weekly_ohlc_df['high'].iloc[-1],
                        'low': weekly_ohlc_df['low'].iloc[-1],
                        'close': weekly_ohlc_df['close'].iloc[-1]
                    }
                else:
                    return None
            elif self.calculation_type == "DAILY":
                # For daily pivot, fetch last available trading day's OHLC (skip holidays/weekends)
                from datetime import datetime, timedelta
                today = datetime.now()
                lookback_days = 1
                max_lookback = 10  # Avoid infinite loop
                ohlc_dict = None
                while lookback_days <= max_lookback:
                    check_date = (today - timedelta(days=lookback_days)).strftime("%Y-%m-%d")
                    ohlc_df = fyers_connect.get_ohlc_data(underlying_symbol, interval="1D", start_date=check_date, end_date=check_date)
                    if not ohlc_df.empty:
                        ohlc_dict = {
                            'high': ohlc_df['high'].iloc[-1],
                            'low': ohlc_df['low'].iloc[-1],
                            'close': ohlc_df['close'].iloc[-1]
                        }
                        break
                    lookback_days += 1
                if ohlc_dict is None:
                    return None
            elif self.calculation_type == "MONTHLY":
                # For monthly pivot, fetch previous month's OHLC and aggregate
                from datetime import datetime, timedelta
                today = datetime.now()
                first_day_of_current_month = today.replace(day=1)
                last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
                first_day_of_previous_month = last_day_of_previous_month.replace(day=1)
                
                start_date_str = first_day_of_previous_month.strftime("%Y-%m-%d")
                end_date_str = last_day_of_previous_month.strftime("%Y-%m-%d")
                
                ohlc_df = fyers_connect.get_ohlc_data(underlying_symbol, interval="1D", start_date=start_date_str, end_date=end_date_str)
                if not ohlc_df.empty:
                    ohlc_dict = {
                        'high': ohlc_df['high'].max(),
                        'low': ohlc_df['low'].min(),
                        'close': ohlc_df['close'].iloc[-1]
                    }
                else:
                    return None
            else:
                logger.error(f"Unsupported calculation_type: {self.calculation_type}")
                return None

            logger.debug(f"Fetched {self.calculation_type} OHLC for {symbol}: {ohlc_dict}")
            return ohlc_dict

        except Exception as e:
            logger.warning(f"Failed to fetch {self.calculation_type} OHLC for {symbol}: {e}")
            return None

    def _get_fyers_connect(self):
        """Get or create cached FyersConnect instance, reusing existing authentication."""
        if self._fyers_connect is None:
            from fyers_connect import FyersConnect
            from fyers_config import FyersConfig

            # Create FyersConnect instance to use the same _fetch_weekly_ohlc_for_option method
            fyers_config = FyersConfig(env_path=self.config.env_path)
            self._fyers_connect = FyersConnect(fyers_config)

            # Reuse existing authentication from self.fyers_client to avoid duplicate login
            if self.fyers_client and self.fyers_client.is_authenticated():
                # Copy authentication details to avoid duplicate login
                self._fyers_connect.access_token = self.fyers_client.access_token
                self._fyers_connect.fyers = self.fyers_client.fyers_api
                logger.debug("Reusing existing Fyers authentication for OHLC fetching")
            else:
                # Fallback: authenticate if not already done
                if not self._fyers_connect.login():
                    logger.warning("Failed to login to FyersConnect for OHLC fetching")
                    self._fyers_connect = None
                    return None

        return self._fyers_connect

    def _fetch_ohlc_for_option(self, symbol: str) -> Optional[Dict[str, float]]:
        """Fetch OHLC data for an option symbol based on calculation_type."""
        try:
            # Check cache first
            cache_key = f"{symbol}_{self.calculation_type}"
            if cache_key in self._ohlc_cache:
                logger.debug(f"Using cached OHLC data for {symbol}")
                return self._ohlc_cache[cache_key]

            # Check if this symbol previously failed
            if symbol in self._failed_symbols:
                logger.debug(f"Skipping {symbol} - previously failed to fetch OHLC data")
                return None

            if not self.fyers_client:
                logger.warning("Fyers client not available for OHLC data fetching")
                self._failed_symbols.add(symbol)
                return None

            # Get cached FyersConnect instance
            fyers_connect = self._get_fyers_connect()
            if not fyers_connect:
                self._failed_symbols.add(symbol)
                return None

            # Fetch OHLC data based on calculation_type
            if self.calculation_type == "WEEKLY":
                # Keep existing weekly functionality unchanged
                ohlc_dict = fyers_connect._fetch_weekly_ohlc_for_option(symbol)
            elif self.calculation_type == "DAILY":
                # For daily pivot, fetch last available trading day's OHLC for the option (skip holidays/weekends)
                from datetime import datetime, timedelta
                today = datetime.now()
                lookback_days = 1
                max_lookback = 10  # Avoid infinite loop
                ohlc_dict = None
                while lookback_days <= max_lookback:
                    check_date = (today - timedelta(days=lookback_days)).strftime("%Y-%m-%d")
                    ohlc_df = fyers_connect.get_ohlc_data(symbol, interval="1D", start_date=check_date, end_date=check_date)
                    if not ohlc_df.empty:
                        ohlc_dict = {
                            'high': ohlc_df['high'].iloc[-1],
                            'low': ohlc_df['low'].iloc[-1],
                            'close': ohlc_df['close'].iloc[-1]
                        }
                        break
                    lookback_days += 1
                if ohlc_dict is None:
                    ohlc_dict = None
            elif self.calculation_type == "MONTHLY":
                # For monthly pivot, fetch previous month's OHLC for the option
                from datetime import datetime, timedelta
                today = datetime.now()
                first_day_of_current_month = today.replace(day=1)
                last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
                first_day_of_previous_month = last_day_of_previous_month.replace(day=1)
                
                start_date_str = first_day_of_previous_month.strftime("%Y-%m-%d")
                end_date_str = last_day_of_previous_month.strftime("%Y-%m-%d")
                
                ohlc_df = fyers_connect.get_ohlc_data(symbol, interval="1D", start_date=start_date_str, end_date=end_date_str)
                if not ohlc_df.empty:
                    ohlc_dict = {
                        'high': ohlc_df['high'].max(),
                        'low': ohlc_df['low'].min(),
                        'close': ohlc_df['close'].iloc[-1]
                    }
                else:
                    ohlc_dict = None
            else:
                logger.error(f"Unsupported calculation_type: {self.calculation_type}")
                return None

            if not ohlc_dict:
                logger.warning(f"No {self.calculation_type} OHLC data available for option {symbol}")
                self._failed_symbols.add(symbol)
                return None

            # Cache successful result
            cache_key = f"{symbol}_{self.calculation_type}"
            self._ohlc_cache[cache_key] = ohlc_dict
            logger.debug(f"Fetched and cached {self.calculation_type} OHLC for option {symbol}: {ohlc_dict}")
            return ohlc_dict

        except Exception as e:
            logger.warning(f"Failed to fetch {self.calculation_type} OHLC for option {symbol}: {e}")
            self._failed_symbols.add(symbol)
            return None

    def calculate_pivot_points_for_options(self, symbol: str, ohlc_data: Dict[str, float]) -> Optional[PivotPointData]:
        """
        Calculate pivot points for options using their OHLC data.
        
        Args:
            symbol: Option symbol
            ohlc_data: Dictionary containing 'high', 'low', 'close' keys
            
        Returns:
            PivotPointData object or None if calculation fails
        """
        if not self.enabled:
            return None
            
        try:
            high = ohlc_data.get('high', 0)
            low = ohlc_data.get('low', 0)
            close = ohlc_data.get('close', 0)
            
            if high <= 0 or low <= 0 or close <= 0:
                logger.warning(f"Invalid OHLC data for {symbol}: {ohlc_data}")
                return None
            
            # Calculate pivot points using the standard formula
            pivot_levels = _calculate_pivot_standard(high, low, close)
            
            # Create pivot point data object
            pivot_data = PivotPointData(pivot_levels=pivot_levels)
            
            # For options, we need the current LTP to calculate distances
            # This should be provided separately or extracted from market data
            
            logger.debug(f"Calculated pivot points for option {symbol}: {pivot_levels}")
            return pivot_data
            
        except Exception as e:
            logger.warning(f"Failed to calculate pivot points for option {symbol}: {e}")
            return None
    
    def _calculate_closest_positive_pivot(self, ltp: float, pivot_data: PivotPointData) -> None:
        """
        Calculate the closest positive pivot level to the current LTP.
        
        Args:
            ltp: Last traded price
            pivot_data: PivotPointData object to update
        """
        if pd.isna(ltp) or ltp <= 0:
            return
        
        positive_pivots = {k: v for k, v in pivot_data.pivot_levels.items() if v > 0}
        
        if not positive_pivots:
            return
        
        closest_pivot = min(positive_pivots.items(), key=lambda x: abs(ltp - x[1]))
        pivot_level, pivot_value = closest_pivot
        distance = abs(ltp - pivot_value)
        
        pivot_data.closest_positive_pivot_level = pivot_level
        pivot_data.closest_positive_pivot_value = pivot_value
        pivot_data.distance_to_positive_pivot = distance
        pivot_data.distance_to_positive_pivot_pct = (distance / pivot_value * 100) if pivot_value > 0 else 0
    
    def _calculate_min_positive_pivot(self, ltp: float, pivot_data: PivotPointData) -> None:
        """
        Calculate the minimum positive pivot level and distance to it.
        
        Args:
            ltp: Last traded price
            pivot_data: PivotPointData object to update
        """
        if pd.isna(ltp) or ltp <= 0:
            return
        
        # Define the pivot levels we're interested in
        pivot_levels = ['Pivot', 'R1', 'R2', 'R3', 'R4', 'R5', 'S1', 'S2', 'S3', 'S4', 'S5']
        positive_pivot_values = []
        
        for level in pivot_levels:
            if level in pivot_data.pivot_levels and pivot_data.pivot_levels[level] > 0:
                positive_pivot_values.append(pivot_data.pivot_levels[level])
        
        if not positive_pivot_values:
            return
        
        # Find the minimum positive pivot value
        min_positive_pivot = min(positive_pivot_values)
        distance = abs(ltp - min_positive_pivot)
        
        # Find which level corresponds to this minimum value
        min_pivot_level = None
        for level in pivot_levels:
            if level in pivot_data.pivot_levels and pivot_data.pivot_levels[level] == min_positive_pivot:
                min_pivot_level = level
                break
        
        pivot_data.min_positive_pivot_level = min_pivot_level
        pivot_data.min_positive_pivot_value = min_positive_pivot
        pivot_data.distance_to_min_positive_pivot = distance
        pivot_data.distance_to_min_positive_pivot_pct = (distance / min_positive_pivot * 100) if min_positive_pivot > 0 else 0
    
    def add_pivot_columns_to_dataframe(self, df: pd.DataFrame, pivot_data_map: Dict[str, PivotPointData]) -> pd.DataFrame:
        """
        Add pivot point columns to a DataFrame.
        
        Args:
            df: DataFrame containing symbol data
            pivot_data_map: Dictionary mapping symbols to their PivotPointData
            
        Returns:
            DataFrame with added pivot point columns
        """
        if not self.enabled or df.empty:
            return df
        
        # Create a copy to avoid modifying the original
        result_df = df.copy()
        
        # Add pivot point columns
        pivot_columns = ['Pivot', 'R1', 'S1', 'R2', 'S2', 'R3', 'S3', 'R4', 'S4', 'R5', 'S5']
        
        # Initialize columns
        for col in pivot_columns:
            result_df[col] = None
        
        # Add distance columns
        result_df['closest_positive_pivot_level'] = None
        result_df['closest_positive_pivot_value'] = None
        result_df['distance_to_positive_pivot'] = None
        result_df['distance_to_positive_pivot_pct'] = None
        result_df['min_positive_pivot_level'] = None
        result_df['min_positive_pivot_value'] = None
        result_df['distance_to_min_positive_pivot'] = None
        result_df['distance_to_min_positive_pivot_pct'] = None
        
        # Fill in the data
        for idx, row in result_df.iterrows():
            symbol = row['symbol']
            if symbol in pivot_data_map:
                pivot_data = pivot_data_map[symbol]
                
                # Fill pivot levels
                for col in pivot_columns:
                    if col in pivot_data.pivot_levels:
                        result_df.at[idx, col] = pivot_data.pivot_levels[col]
                
                # Fill distance data
                result_df.at[idx, 'closest_positive_pivot_level'] = pivot_data.closest_positive_pivot_level
                result_df.at[idx, 'closest_positive_pivot_value'] = pivot_data.closest_positive_pivot_value
                result_df.at[idx, 'distance_to_positive_pivot'] = pivot_data.distance_to_positive_pivot
                result_df.at[idx, 'distance_to_positive_pivot_pct'] = pivot_data.distance_to_positive_pivot_pct
                result_df.at[idx, 'min_positive_pivot_level'] = pivot_data.min_positive_pivot_level
                result_df.at[idx, 'min_positive_pivot_value'] = pivot_data.min_positive_pivot_value
                result_df.at[idx, 'distance_to_min_positive_pivot'] = pivot_data.distance_to_min_positive_pivot
                result_df.at[idx, 'distance_to_min_positive_pivot_pct'] = pivot_data.distance_to_min_positive_pivot_pct
        
        return result_df
    
    def filter_by_closest_pivot_points(self, filtered_symbols: List[Any],
                                     pivot_data_map: Dict[str, PivotPointData]) -> List[Any]:
        """
        Filter symbols by their proximity to pivot points.
        
        Args:
            filtered_symbols: List of filtered symbols
            pivot_data_map: Dictionary mapping symbols to their PivotPointData
            
        Returns:
            Filtered list of symbols based on pivot point proximity
        """
        if not self.enabled or not filtered_symbols:
            return filtered_symbols
        
        # Create list of symbols with valid pivot data and distances
        symbols_with_pivots = []
        
        for symbol_obj in filtered_symbols:
            symbol = symbol_obj.symbol
            if symbol in pivot_data_map:
                pivot_data = pivot_data_map[symbol]
                if (pivot_data.distance_to_min_positive_pivot is not None and 
                    pivot_data.distance_to_min_positive_pivot != float('inf')):
                    symbols_with_pivots.append((symbol_obj, pivot_data.distance_to_min_positive_pivot))
        
        if not symbols_with_pivots:
            logger.warning("No symbols with valid pivot point data found")
            return []
        
        # Sort by distance to minimum positive pivot
        symbols_with_pivots.sort(key=lambda x: x[1])
        
        # Take top N closest
        top_symbols = symbols_with_pivots[:self.top_n_closest]
        
        logger.info(f"Filtered to top {len(top_symbols)} symbols closest to minimum positive pivot points")
        
        return [symbol_obj for symbol_obj, _ in top_symbols]
